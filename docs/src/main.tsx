import React from "react";
import ReactDOM from "react-dom/client";
import { <PERSON><PERSON>, ThemeProvider, useTheme } from "@nui/ui";

type ThemeMode = "light" | "dark" | "auto";

function App() {
  const {
    config: { mode, variant },
    isDark,
    availableVariants,
    setMode,
    setVariant,
    toggleMode,
    cycleVariant,
  } = useTheme();

  // Debug log to see detected variants
  console.log("Available variants:", availableVariants);

  return (
    <div className="min-h-screen p-8 bg-background text-foreground">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold">Theme System Demo</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-4 bg-card border rounded-lg space-y-2 text-sm">
            <h2 className="text-xl font-semibold mb-3">Current Theme</h2>
            <div>
              Mode: <code>{mode}</code>
            </div>
            <div>
              Variant: <code>{variant}</code>
            </div>
            <div>
              Is Dark: <code>{isDark.toString()}</code>
            </div>
            <div>
              Available: <code>{availableVariants.join(", ")}</code>
            </div>
          </div>

          <div className="p-4 bg-card border rounded-lg space-y-3">
            <h2 className="text-xl font-semibold mb-3">Theme Controls</h2>
            <Button onClick={toggleMode} className="w-full">
              Toggle Mode ({mode})
            </Button>
            <Button
              onClick={() => cycleVariant(availableVariants)}
              className="w-full"
              variant="secondary"
            >
              Cycle Variant ({variant})
            </Button>

            <div className="grid grid-cols-3 gap-2">
              {(["light", "dark", "auto"] as ThemeMode[]).map((m) => (
                <Button
                  key={m}
                  onClick={() => setMode(m)}
                  variant={mode === m ? "default" : "outline"}
                  size="sm"
                >
                  {m}
                </Button>
              ))}
            </div>

            <div className="grid grid-cols-2 gap-2">
              {availableVariants.map((v) => (
                <Button
                  key={v}
                  onClick={() => setVariant(v)}
                  variant={variant === v ? "default" : "outline"}
                  size="sm"
                >
                  {v}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ThemeProvider>
      <App />
    </ThemeProvider>
  </React.StrictMode>
);
