import type { ThemeConfig, ThemeVariant } from "../types/theme";

let mediaQuery: MediaQueryList | null = null;
let listener: (() => void) | null = null;

export function applyTheme(config: ThemeConfig) {
  const root = document.documentElement;
  root.setAttribute("data-theme-mode", config.mode);
  root.setAttribute("data-theme-variant", config.variant);
}

export function listenToSystemTheme(callback: () => void) {
  mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
  listener = () => callback();
  mediaQuery.addEventListener("change", listener);
}

export function removeSystemThemeListener() {
  if (mediaQuery && listener) {
    mediaQuery.removeEventListener("change", listener);
  }
}

/**
 * Dynamically detects available theme variants from CSS stylesheets
 * by scanning for CSS classes that match the pattern .theme-{name}
 */
export function detectAvailableVariants(): ThemeVariant[] {
  if (typeof window === "undefined" || typeof document === "undefined") {
    return ["default"];
  }

  const variants = new Set<string>();

  try {
    // Scan all stylesheets for theme variant classes
    for (const stylesheet of document.styleSheets) {
      try {
        // Skip external stylesheets that might cause CORS issues
        if (
          stylesheet.href &&
          !stylesheet.href.startsWith(window.location.origin)
        ) {
          continue;
        }

        const rules = stylesheet.cssRules;
        if (!rules) continue;

        for (const rule of rules) {
          if (rule instanceof CSSStyleRule) {
            const selector = rule.selectorText;

            // Match .theme-{name} patterns (with or without additional selectors)
            const themeMatch = selector.match(/\.theme-([a-zA-Z0-9-_]+)/g);

            if (themeMatch) {
              themeMatch.forEach((match) => {
                const variant = match.replace(".theme-", "");
                // Only add if it's a valid variant name (no special characters except hyphens/underscores)
                if (/^[a-zA-Z0-9-_]+$/.test(variant)) {
                  variants.add(variant);
                }
              });
            }
          }
        }
      } catch {
        // Skip stylesheets that can't be accessed (CORS, etc.)
        continue;
      }
    }
  } catch (e) {
    console.warn("Failed to detect theme variants from stylesheets:", e);
  }

  // Always include 'default' as a fallback
  variants.add("default");

  return Array.from(variants).sort();
}
