import type { ThemeConfig } from "../types/theme";

let mediaQuery: MediaQueryList | null = null;
let listener: (() => void) | null = null;

export function applyTheme(config: ThemeConfig) {
  const root = document.documentElement;
  root.setAttribute("data-theme-mode", config.mode);
  root.setAttribute("data-theme-variant", config.variant);
}

export function listenToSystemTheme(callback: () => void) {
  mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
  listener = () => callback();
  mediaQuery.addEventListener("change", listener);
}

export function removeSystemThemeListener() {
  if (mediaQuery && listener) {
    mediaQuery.removeEventListener("change", listener);
  }
}
