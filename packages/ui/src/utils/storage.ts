import type { ThemeConfig } from "../types/theme";

export function getStoredTheme(key: string): Partial<ThemeConfig> {
  try {
    const value = localStorage.getItem(key);
    if (!value) return {};
    return JSON.parse(value);
  } catch {
    return {};
  }
}

export function persistTheme(config: ThemeConfig, key: string) {
  try {
    localStorage.setItem(key, JSON.stringify(config));
  } catch {
    // ignore
  }
}
