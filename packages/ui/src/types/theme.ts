export type ThemeMode = "light" | "dark" | "auto";
export type ThemeVariant = string;

export interface ThemeConfig {
  mode: ThemeMode;
  variant: ThemeVariant;
}

export interface ThemeContextValue {
  config: ThemeConfig;
  isDark: boolean;
  availableVariants: ThemeVariant[];
  setConfig: (config: ThemeConfig) => void;
  setMode: (mode: ThemeMode) => void;
  toggleMode: () => void;
  setVariant: (variant: ThemeVariant) => void;
  cycleVariant: (availableVariants: ThemeVariant[]) => void;
}
