import {
  useCallback,
  useEffect,
  useMemo,
  useState,
  useSyncExternalStore,
} from "react";

import { ThemeContext } from "../context/theme-context";
import {
  DEFAULT_CONFIG,
  DEFAULT_VARIANTS,
  THEME_MODES,
} from "../lib/constants";
import type {
  ThemeConfig,
  ThemeContextValue,
  ThemeMode,
  ThemeVariant,
} from "../types/theme";
import { getStoredTheme, persistTheme } from "../utils/storage";
import {
  applyTheme,
  listenToSystemTheme,
  removeSystemThemeListener,
} from "../utils/theme";

interface Props {
  children: React.ReactNode;
  storageKey?: string;
  disableStorage?: boolean;
  defaultTheme?: Partial<ThemeConfig>;
  availableVariants?: ThemeVariant[];
}

export function ThemeProvider({
  children,
  storageKey = "theme",
  disableStorage = false,
  defaultTheme = {},
  availableVariants = DEFAULT_VARIANTS,
}: Props) {
  const [config, setConfig] = useState<ThemeConfig>(() => ({
    ...DEFAULT_CONFIG,
    ...(disableStorage ? {} : getStoredTheme(storageKey)),
    ...defaultTheme,
  }));

  // Track dark mode via sync hook
  const isDark = useSyncExternalStore(
    (cb) => {
      const media = window.matchMedia("(prefers-color-scheme: dark)");
      media.addEventListener("change", cb);
      return () => media.removeEventListener("change", cb);
    },
    () =>
      config.mode === "dark" ||
      (config.mode === "auto" &&
        window.matchMedia("(prefers-color-scheme: dark)").matches),
  );

  useEffect(() => {
    applyTheme(config);
    if (!disableStorage) persistTheme(config, storageKey);
  }, [config, disableStorage, storageKey]);

  useEffect(() => {
    if (config.mode !== "auto") return;
    listenToSystemTheme(() => applyTheme(config));
    return () => removeSystemThemeListener();
  }, [config]);

  const setMode = useCallback((mode: ThemeMode) => {
    setConfig((prev) => ({ ...prev, mode }));
  }, []);

  const toggleMode = useCallback(() => {
    setConfig((prev) => {
      const index = THEME_MODES.indexOf(prev.mode);
      const next = THEME_MODES[(index + 1) % THEME_MODES.length];
      return { ...prev, mode: next };
    });
  }, []);

  const setVariant = useCallback((variant: ThemeVariant) => {
    setConfig((prev) => ({ ...prev, variant }));
  }, []);

  const cycleVariant = useCallback((variants: ThemeVariant[]) => {
    setConfig((prev) => {
      const index = variants.indexOf(prev.variant);
      return {
        ...prev,
        variant: variants[(index + 1) % variants.length],
      };
    });
  }, []);

  const value = useMemo<ThemeContextValue>(
    () => ({
      config,
      isDark,
      availableVariants,
      setConfig,
      setMode,
      toggleMode,
      setVariant,
      cycleVariant,
    }),
    [config, isDark, availableVariants],
  );

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
}
